import Link from 'next/link'
import { 
  MessageSquare, 
  Image, 
  Code, 
  Music, 
  Video, 
  FileText,
  Palette,
  Brain,
  Mic,
  Search,
  BarChart,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const categories = [
  {
    id: 'text',
    name: '文本生成',
    description: '写作助手、内容创作、翻译工具等，帮助你创作各种文本内容',
    icon: MessageSquare,
    count: 85,
    color: 'bg-blue-500',
    tools: ['ChatGPT', 'Claude', 'Notion AI', 'Jasper']
  },
  {
    id: 'image',
    name: '图像处理',
    description: '图像生成、编辑、设计工具等，释放你的创意潜能',
    icon: Image,
    count: 72,
    color: 'bg-purple-500',
    tools: ['Midjourney', 'DALL-E', 'Stable Diffusion', 'Canva AI']
  },
  {
    id: 'code',
    name: '代码助手',
    description: '编程辅助、代码生成工具等，提升开发效率',
    icon: Code,
    count: 45,
    color: 'bg-green-500',
    tools: ['GitHub Copilot', 'Cursor', 'Tabnine', 'CodeT5']
  },
  {
    id: 'audio',
    name: '音频处理',
    description: '语音合成、音乐生成工具等，创造美妙的声音',
    icon: Music,
    count: 38,
    color: 'bg-orange-500',
    tools: ['ElevenLabs', 'Mubert', 'AIVA', 'Speechify']
  },
  {
    id: 'video',
    name: '视频制作',
    description: '视频生成、编辑、特效工具等，制作精彩视频内容',
    icon: Video,
    count: 29,
    color: 'bg-red-500',
    tools: ['Runway', 'Synthesia', 'Pictory', 'Luma AI']
  },
  {
    id: 'productivity',
    name: '生产力工具',
    description: '办公助手、自动化工具等，提升工作效率',
    icon: FileText,
    count: 56,
    color: 'bg-indigo-500',
    tools: ['Notion AI', 'Zapier', 'Monday.com', 'Grammarly']
  },
  {
    id: 'design',
    name: '设计工具',
    description: 'UI设计、创意设计工具等，打造精美设计',
    icon: Palette,
    count: 34,
    color: 'bg-pink-500',
    tools: ['Figma AI', 'Framer', 'Adobe Firefly', 'Uizard']
  },
  {
    id: 'research',
    name: '研究分析',
    description: '数据分析、研究助手等，深入洞察数据',
    icon: Brain,
    count: 41,
    color: 'bg-teal-500',
    tools: ['Perplexity', 'Semantic Scholar', 'Elicit', 'Research Rabbit']
  },
  {
    id: 'voice',
    name: '语音识别',
    description: '语音转文字、语音助手等，智能语音处理',
    icon: Mic,
    count: 23,
    color: 'bg-cyan-500',
    tools: ['Whisper', 'Otter.ai', 'Rev', 'Descript']
  },
  {
    id: 'search',
    name: '搜索工具',
    description: '智能搜索、信息检索等，快速找到所需信息',
    icon: Search,
    count: 18,
    color: 'bg-yellow-500',
    tools: ['Perplexity', 'You.com', 'Phind', 'Kagi']
  },
  {
    id: 'analytics',
    name: '数据分析',
    description: '商业智能、数据可视化等，让数据说话',
    icon: BarChart,
    count: 27,
    color: 'bg-emerald-500',
    tools: ['Tableau', 'Power BI', 'DataRobot', 'H2O.ai']
  },
  {
    id: 'automation',
    name: '自动化',
    description: '工作流自动化、机器人流程等，解放双手',
    icon: Zap,
    count: 31,
    color: 'bg-violet-500',
    tools: ['Zapier', 'Make', 'UiPath', 'Automation Anywhere']
  }
]

export default function CategoriesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">工具分类</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          按功能分类浏览AI工具，快速找到你需要的工具类型
        </p>
      </div>

      {/* 分类网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {categories.map((category) => {
          const Icon = category.icon
          return (
            <Link key={category.id} href={`/categories/${category.id}`}>
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-4">
                    <div className={`w-16 h-16 rounded-xl ${category.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl group-hover:text-primary transition-colors">
                        {category.name}
                      </CardTitle>
                      <div className="text-sm text-muted-foreground">
                        {category.count} 个工具
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <CardDescription className="text-base leading-relaxed">
                    {category.description}
                  </CardDescription>

                  {/* 热门工具预览 */}
                  <div>
                    <div className="text-sm font-medium text-muted-foreground mb-2">
                      热门工具：
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {category.tools.slice(0, 3).map((tool) => (
                        <span
                          key={tool}
                          className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md"
                        >
                          {tool}
                        </span>
                      ))}
                      {category.tools.length > 3 && (
                        <span className="px-2 py-1 text-xs text-muted-foreground">
                          +{category.tools.length - 3} 更多
                        </span>
                      )}
                    </div>
                  </div>

                  {/* 查看更多指示器 */}
                  <div className="flex items-center justify-between pt-2">
                    <div className="text-sm text-primary group-hover:underline">
                      查看所有工具 →
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>

      {/* 底部统计 */}
      <div className="mt-16 text-center">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
          <div>
            <div className="text-3xl font-bold text-primary">500+</div>
            <div className="text-sm text-muted-foreground">总工具数</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">12</div>
            <div className="text-sm text-muted-foreground">主要分类</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">10K+</div>
            <div className="text-sm text-muted-foreground">用户</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">4.8</div>
            <div className="text-sm text-muted-foreground">平均评分</div>
          </div>
        </div>
      </div>
    </div>
  )
}
