import Link from 'next/link'
import { 
  MessageSquare, 
  Image, 
  Code, 
  Music, 
  Video, 
  FileText,
  Palette,
  Brain,
  Mic,
  Search,
  BarChart,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const categories = [
  {
    id: 'text',
    name: '文本生成',
    description: '写作助手、内容创作、翻译工具',
    icon: MessageSquare,
    count: 85,
    color: 'bg-blue-500'
  },
  {
    id: 'image',
    name: '图像处理',
    description: '图像生成、编辑、设计工具',
    icon: Image,
    count: 72,
    color: 'bg-purple-500'
  },
  {
    id: 'code',
    name: '代码助手',
    description: '编程辅助、代码生成工具',
    icon: Code,
    count: 45,
    color: 'bg-green-500'
  },
  {
    id: 'audio',
    name: '音频处理',
    description: '语音合成、音乐生成工具',
    icon: Music,
    count: 38,
    color: 'bg-orange-500'
  },
  {
    id: 'video',
    name: '视频制作',
    description: '视频生成、编辑、特效工具',
    icon: Video,
    count: 29,
    color: 'bg-red-500'
  },
  {
    id: 'productivity',
    name: '生产力工具',
    description: '办公助手、自动化工具',
    icon: FileText,
    count: 56,
    color: 'bg-indigo-500'
  },
  {
    id: 'design',
    name: '设计工具',
    description: 'UI设计、创意设计工具',
    icon: Palette,
    count: 34,
    color: 'bg-pink-500'
  },
  {
    id: 'research',
    name: '研究分析',
    description: '数据分析、研究助手',
    icon: Brain,
    count: 41,
    color: 'bg-teal-500'
  },
  {
    id: 'voice',
    name: '语音识别',
    description: '语音转文字、语音助手',
    icon: Mic,
    count: 23,
    color: 'bg-cyan-500'
  },
  {
    id: 'search',
    name: '搜索工具',
    description: '智能搜索、信息检索',
    icon: Search,
    count: 18,
    color: 'bg-yellow-500'
  },
  {
    id: 'analytics',
    name: '数据分析',
    description: '商业智能、数据可视化',
    icon: BarChart,
    count: 27,
    color: 'bg-emerald-500'
  },
  {
    id: 'automation',
    name: '自动化',
    description: '工作流自动化、机器人流程',
    icon: Zap,
    count: 31,
    color: 'bg-violet-500'
  }
]

export function CategoriesSection() {
  return (
    <section className="py-16 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">工具分类</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            按功能分类浏览，快速找到你需要的AI工具
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => {
            const Icon = category.icon
            return (
              <Link key={category.id} href={`/categories/${category.id}`}>
                <Card className="group hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`w-12 h-12 rounded-lg ${category.color} flex items-center justify-center`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg group-hover:text-primary transition-colors">
                          {category.name}
                        </CardTitle>
                        <div className="text-sm text-muted-foreground">
                          {category.count} 个工具
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <CardDescription>
                      {category.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </Link>
            )
          })}
        </div>

        <div className="text-center mt-12">
          <Link 
            href="/categories"
            className="inline-flex items-center text-primary hover:underline font-medium"
          >
            查看所有分类 →
          </Link>
        </div>
      </div>
    </section>
  )
}
