# AI Tools Navigator - AI工具收录导航网站

一个专门收录和展示各类AI工具的导航网站，帮助用户快速发现、了解和使用优质的AI工具。

## 🚀 功能特性

- **工具展示**：精美的卡片式展示，包含工具信息、评分、标签等
- **分类浏览**：按功能分类浏览，快速找到需要的工具类型
- **智能搜索**：支持按名称、描述、标签搜索工具
- **用户系统**：用户注册、登录、收藏、评价功能
- **工具提交**：用户可以提交新的AI工具
- **响应式设计**：完美适配桌面端和移动端

## 🛠️ 技术栈

### 前端
- **Next.js 14** - React全栈框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Shadcn/ui** - UI组件库
- **Lucide React** - 图标库

### 后端
- **Supabase** - 后端即服务
  - PostgreSQL数据库
  - 用户认证
  - 实时订阅
  - 文件存储

### 部署
- **Vercel** - 前端部署
- **Supabase** - 数据库和后端服务

## 📦 安装和运行

### 前置要求
- Node.js 18+ 
- npm 或 yarn 或 pnpm

### 1. 克隆项目
```bash
git clone <repository-url>
cd ai-tools-navigator
```

### 2. 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 3. 设置Supabase

1. 访问 [Supabase](https://supabase.com) 创建新项目
2. 在项目设置中获取以下信息：
   - Project URL
   - Anon Key
   - Service Role Key

3. 复制环境变量文件：
```bash
cp .env.local.example .env.local
```

4. 编辑 `.env.local` 文件，填入Supabase配置：
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. 设置数据库

1. 在Supabase项目的SQL编辑器中执行 `supabase/schema.sql` 创建表结构
2. 执行 `supabase/seed.sql` 插入示例数据

### 5. 运行开发服务器
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 📁 项目结构

```
ai-tools-navigator/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # React组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── layout/           # 布局组件
│   │   └── home/             # 首页组件
│   └── lib/                  # 工具函数和配置
│       ├── supabase.ts       # Supabase客户端
│       └── utils.ts          # 工具函数
├── supabase/                 # 数据库脚本
│   ├── schema.sql           # 数据库结构
│   └── seed.sql             # 示例数据
├── public/                  # 静态文件
├── package.json            # 项目配置
├── tailwind.config.ts      # Tailwind配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 项目说明
```

## 🗄️ 数据库设计

### 主要表结构

- **categories** - 工具分类
- **tools** - AI工具信息
- **reviews** - 用户评价
- **favorites** - 用户收藏

详细的数据库结构请查看 `supabase/schema.sql` 文件。

## 🚀 部署

### Vercel部署

1. 将代码推送到GitHub仓库
2. 在Vercel中导入项目
3. 设置环境变量（与本地开发相同）
4. 部署

### 环境变量设置

在Vercel项目设置中添加以下环境变量：
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub Issues: [项目Issues页面]

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
