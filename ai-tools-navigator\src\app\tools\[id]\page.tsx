'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Star, ExternalLink, Heart, Share2, Eye, MessageSquare } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tool } from '@/lib/supabase'

// 模拟工具详情数据
const mockToolDetail: Tool = {
  id: '1',
  name: 'ChatGPT',
  description: 'ChatGPT是由OpenAI开发的一款基于GPT-4架构的大型语言模型。它能够进行自然语言对话、回答各种问题、协助写作、代码生成、翻译、总结等多种任务。ChatGPT具有强大的理解能力和生成能力，能够根据上下文进行连贯的多轮对话，是目前最受欢迎的AI助手之一。\n\n主要特点包括：\n• 自然语言理解和生成\n• 多轮对话能力\n• 代码编写和调试\n• 文本创作和编辑\n• 语言翻译\n• 数学计算和推理\n• 创意写作和头脑风暴',
  short_description: '最强大的对话AI助手',
  website_url: 'https://chat.openai.com',
  logo_url: 'https://via.placeholder.com/128/00D4AA/FFFFFF?text=GPT',
  category_id: '1',
  pricing_type: 'freemium',
  pricing_details: '免费版本提供基础功能，ChatGPT Plus ($20/月) 提供GPT-4访问、优先访问权、更快响应速度等高级功能。',
  features: [
    '自然语言对话',
    '代码生成和调试',
    '文本创作和编辑',
    '问题解答',
    '语言翻译',
    '数学计算',
    '创意写作',
    '学习辅导'
  ],
  tags: ['对话', 'AI助手', '文本生成', 'OpenAI', 'GPT-4'],
  rating: 4.8,
  review_count: 1250,
  view_count: 15420,
  is_featured: true,
  status: 'published',
  created_at: '2024-01-01',
  updated_at: '2024-01-01',
  category: {
    id: '1',
    name: '文本生成',
    description: '写作助手、内容创作、翻译工具',
    icon: 'MessageSquare',
    slug: 'text',
    sort_order: 1,
    created_at: '2024-01-01'
  }
}

export default function ToolDetailPage() {
  const params = useParams()
  const [tool, setTool] = useState<Tool | null>(null)
  const [isFavorited, setIsFavorited] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      setTool(mockToolDetail)
      setLoading(false)
    }, 500)
  }, [params.id])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-6">
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!tool) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">工具未找到</h1>
        <p className="text-muted-foreground mb-8">抱歉，您访问的工具不存在或已被删除。</p>
        <Button asChild>
          <Link href="/">返回首页</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
        <Link href="/" className="hover:text-primary">首页</Link>
        <span>/</span>
        <Link href="/tools" className="hover:text-primary">工具</Link>
        <span>/</span>
        <Link href={`/categories/${tool.category?.slug}`} className="hover:text-primary">
          {tool.category?.name}
        </Link>
        <span>/</span>
        <span className="text-foreground">{tool.name}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2 space-y-8">
          {/* 工具头部信息 */}
          <div className="flex items-start space-x-6">
            <div className="w-24 h-24 rounded-xl overflow-hidden bg-muted flex items-center justify-center flex-shrink-0">
              <img 
                src={tool.logo_url} 
                alt={tool.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 space-y-4">
              <div>
                <h1 className="text-3xl font-bold mb-2">{tool.name}</h1>
                <p className="text-lg text-muted-foreground">{tool.short_description}</p>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-1">
                  <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  <span className="font-semibold">{tool.rating}</span>
                  <span className="text-muted-foreground">({tool.review_count} 评价)</span>
                </div>
                <div className="flex items-center space-x-1 text-muted-foreground">
                  <Eye className="h-4 w-4" />
                  <span>{tool.view_count.toLocaleString()} 次查看</span>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button asChild>
                  <Link href={tool.website_url} target="_blank">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    访问官网
                  </Link>
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setIsFavorited(!isFavorited)}
                >
                  <Heart className={`h-4 w-4 mr-2 ${isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                  {isFavorited ? '已收藏' : '收藏'}
                </Button>
                <Button variant="outline">
                  <Share2 className="h-4 w-4 mr-2" />
                  分享
                </Button>
              </div>
            </div>
          </div>

          {/* 工具描述 */}
          <Card>
            <CardHeader>
              <CardTitle>工具介绍</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                {tool.description.split('\n').map((paragraph, index) => (
                  <p key={index} className="mb-4 last:mb-0">
                    {paragraph}
                  </p>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 功能特点 */}
          <Card>
            <CardHeader>
              <CardTitle>主要功能</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {tool.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 用户评价 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5" />
                <span>用户评价</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无评价，成为第一个评价的用户吧！</p>
                <Button className="mt-4">写评价</Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm text-muted-foreground mb-1">分类</div>
                <Link 
                  href={`/categories/${tool.category?.slug}`}
                  className="text-primary hover:underline"
                >
                  {tool.category?.name}
                </Link>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground mb-1">价格模式</div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  tool.pricing_type === 'free' 
                    ? 'bg-green-100 text-green-800' 
                    : tool.pricing_type === 'freemium'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-orange-100 text-orange-800'
                }`}>
                  {tool.pricing_type === 'free' ? '免费' : 
                   tool.pricing_type === 'freemium' ? '免费试用' : '付费'}
                </span>
              </div>

              <div>
                <div className="text-sm text-muted-foreground mb-1">价格详情</div>
                <p className="text-sm">{tool.pricing_details}</p>
              </div>

              <div>
                <div className="text-sm text-muted-foreground mb-2">标签</div>
                <div className="flex flex-wrap gap-1">
                  {tool.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 相关工具推荐 */}
          <Card>
            <CardHeader>
              <CardTitle>相关工具</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-4 text-muted-foreground">
                <p className="text-sm">暂无相关工具推荐</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
