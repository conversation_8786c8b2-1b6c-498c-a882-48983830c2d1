'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Star, ExternalLink, Heart } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tool } from '@/lib/supabase'

// 模拟数据 - 实际项目中会从Supabase获取
const mockFeaturedTools: Tool[] = [
  {
    id: '1',
    name: 'ChatGPT',
    description: 'OpenAI开发的强大对话AI，能够进行自然语言对话、回答问题、协助写作等多种任务。',
    short_description: '最强大的对话AI助手',
    website_url: 'https://chat.openai.com',
    logo_url: 'https://via.placeholder.com/64/00D4AA/FFFFFF?text=GPT',
    category_id: '1',
    pricing_type: 'freemium',
    pricing_details: '免费版本 + $20/月专业版',
    features: ['自然语言对话', '代码生成', '文本创作', '问题解答'],
    tags: ['对话', 'AI助手', '文本生成'],
    rating: 4.8,
    review_count: 1250,
    view_count: 15420,
    is_featured: true,
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '2',
    name: 'Midjourney',
    description: '基于AI的图像生成工具，能够根据文本描述创造出令人惊艳的艺术作品和图像。',
    short_description: '顶级AI图像生成工具',
    website_url: 'https://midjourney.com',
    logo_url: 'https://via.placeholder.com/64/FF6B6B/FFFFFF?text=MJ',
    category_id: '2',
    pricing_type: 'paid',
    pricing_details: '$10-60/月订阅制',
    features: ['文本到图像', '艺术创作', '高质量输出', '多种风格'],
    tags: ['图像生成', 'AI艺术', '创意设计'],
    rating: 4.7,
    review_count: 890,
    view_count: 12340,
    is_featured: true,
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '3',
    name: 'GitHub Copilot',
    description: '由GitHub和OpenAI联合开发的AI编程助手，能够实时提供代码建议和自动补全。',
    short_description: 'AI编程助手',
    website_url: 'https://github.com/features/copilot',
    logo_url: 'https://via.placeholder.com/64/4078C0/FFFFFF?text=GH',
    category_id: '3',
    pricing_type: 'paid',
    pricing_details: '$10/月个人版',
    features: ['代码自动补全', '函数生成', '注释生成', '多语言支持'],
    tags: ['编程', '代码助手', '开发工具'],
    rating: 4.6,
    review_count: 2100,
    view_count: 18900,
    is_featured: true,
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '4',
    name: 'Notion AI',
    description: 'Notion内置的AI助手，能够帮助用户进行写作、总结、翻译等多种文档处理任务。',
    short_description: '智能文档助手',
    website_url: 'https://notion.so/ai',
    logo_url: 'https://via.placeholder.com/64/000000/FFFFFF?text=N',
    category_id: '1',
    pricing_type: 'freemium',
    pricing_details: '免费试用 + $10/月',
    features: ['智能写作', '内容总结', '语言翻译', '文档整理'],
    tags: ['文档', '写作助手', '生产力'],
    rating: 4.5,
    review_count: 750,
    view_count: 9800,
    is_featured: true,
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }
]

export function FeaturedTools() {
  const [tools, setTools] = useState<Tool[]>([])

  useEffect(() => {
    // 模拟API调用
    setTools(mockFeaturedTools)
  }, [])

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">精选AI工具</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            我们精心挑选的最受欢迎和最实用的AI工具
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {tools.map((tool) => (
            <Card key={tool.id} className="group hover:shadow-lg transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                      <img 
                        src={tool.logo_url} 
                        alt={tool.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                      <div className="flex items-center space-x-1 mt-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm text-muted-foreground">
                          {tool.rating} ({tool.review_count})
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <CardDescription className="line-clamp-2">
                  {tool.short_description}
                </CardDescription>

                <div className="flex flex-wrap gap-1">
                  {tool.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      tool.pricing_type === 'free' 
                        ? 'bg-green-100 text-green-800' 
                        : tool.pricing_type === 'freemium'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {tool.pricing_type === 'free' ? '免费' : 
                       tool.pricing_type === 'freemium' ? '免费试用' : '付费'}
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/tools/${tool.id}`}>
                        详情
                      </Link>
                    </Button>
                    <Button size="sm" asChild>
                      <Link href={tool.website_url} target="_blank">
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg" asChild>
            <Link href="/tools">
              查看更多工具
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
