'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Search, Filter, Grid, List, Star, ExternalLink, Heart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tool } from '@/lib/supabase'

// 模拟工具列表数据
const mockTools: Tool[] = [
  {
    id: '1',
    name: 'ChatGPT',
    description: 'OpenAI开发的强大对话AI，能够进行自然语言对话、回答问题、协助写作等多种任务。',
    short_description: '最强大的对话AI助手',
    website_url: 'https://chat.openai.com',
    logo_url: 'https://via.placeholder.com/64/00D4AA/FFFFFF?text=GPT',
    category_id: '1',
    pricing_type: 'freemium',
    pricing_details: '免费版本 + $20/月专业版',
    features: ['自然语言对话', '代码生成', '文本创作', '问题解答'],
    tags: ['对话', 'AI助手', '文本生成'],
    rating: 4.8,
    review_count: 1250,
    view_count: 15420,
    is_featured: true,
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '2',
    name: 'Midjourney',
    description: '基于AI的图像生成工具，能够根据文本描述创造出令人惊艳的艺术作品和图像。',
    short_description: '顶级AI图像生成工具',
    website_url: 'https://midjourney.com',
    logo_url: 'https://via.placeholder.com/64/FF6B6B/FFFFFF?text=MJ',
    category_id: '2',
    pricing_type: 'paid',
    pricing_details: '$10-60/月订阅制',
    features: ['文本到图像', '艺术创作', '高质量输出', '多种风格'],
    tags: ['图像生成', 'AI艺术', '创意设计'],
    rating: 4.7,
    review_count: 890,
    view_count: 12340,
    is_featured: true,
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  // 可以添加更多工具...
]

export default function ToolsPage() {
  const [tools, setTools] = useState<Tool[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      setTools(mockTools)
      setLoading(false)
    }, 500)
  }, [])

  const filteredTools = tools.filter(tool =>
    tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tool.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">AI工具大全</h1>
        <p className="text-muted-foreground">
          发现和探索各种优质的AI工具，提升你的工作效率
        </p>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="搜索AI工具..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 搜索结果统计 */}
      <div className="mb-6">
        <p className="text-muted-foreground">
          找到 {filteredTools.length} 个工具
          {searchQuery && (
            <span> 包含 "{searchQuery}"</span>
          )}
        </p>
      </div>

      {/* 工具列表 */}
      {filteredTools.length === 0 ? (
        <div className="text-center py-12">
          <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="text-lg font-semibold mb-2">未找到相关工具</h3>
          <p className="text-muted-foreground mb-4">
            尝试使用不同的关键词搜索，或者浏览所有工具
          </p>
          <Button onClick={() => setSearchQuery('')}>
            清除搜索
          </Button>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredTools.map((tool) => (
            <Card key={tool.id} className="group hover:shadow-lg transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                      <img 
                        src={tool.logo_url} 
                        alt={tool.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                      <div className="flex items-center space-x-1 mt-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm text-muted-foreground">
                          {tool.rating} ({tool.review_count})
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <CardDescription className="line-clamp-2">
                  {tool.short_description}
                </CardDescription>

                <div className="flex flex-wrap gap-1">
                  {tool.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      tool.pricing_type === 'free' 
                        ? 'bg-green-100 text-green-800' 
                        : tool.pricing_type === 'freemium'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {tool.pricing_type === 'free' ? '免费' : 
                       tool.pricing_type === 'freemium' ? '免费试用' : '付费'}
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/tools/${tool.id}`}>
                        详情
                      </Link>
                    </Button>
                    <Button size="sm" asChild>
                      <Link href={tool.website_url} target="_blank">
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 分页 */}
      {filteredTools.length > 0 && (
        <div className="flex justify-center mt-12">
          <div className="flex space-x-2">
            <Button variant="outline" disabled>
              上一页
            </Button>
            <Button variant="outline" className="bg-primary text-primary-foreground">
              1
            </Button>
            <Button variant="outline">
              2
            </Button>
            <Button variant="outline">
              3
            </Button>
            <Button variant="outline">
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
