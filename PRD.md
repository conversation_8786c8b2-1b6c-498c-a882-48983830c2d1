# AI工具收录导航网站 - 产品需求文档

## 1. 产品概述

### 1.1 产品名称
AI Tools Navigator (AI工具导航)

### 1.2 产品定位
一个专门收录和展示各类AI工具的导航网站，帮助用户快速发现、了解和使用优质的AI工具。

### 1.3 目标用户
- AI爱好者和从业者
- 开发者和技术人员
- 内容创作者
- 企业决策者
- 学生和研究人员

## 2. 核心功能需求

### 2.1 工具展示功能
- **工具卡片展示**：每个工具以卡片形式展示，包含图标、名称、简介、标签等
- **分类浏览**：按功能分类（如文本生成、图像处理、代码助手等）
- **搜索功能**：支持按名称、描述、标签搜索工具
- **筛选功能**：按价格模式（免费/付费）、评分等筛选

### 2.2 工具详情功能
- **详细信息**：工具描述、功能特点、使用场景
- **基本信息**：官网链接、价格信息、支持平台
- **用户评价**：评分和评论系统
- **相关工具推荐**：推荐类似或互补的工具

### 2.3 用户交互功能
- **收藏功能**：用户可以收藏喜欢的工具
- **评分评论**：用户可以对工具进行评分和评论
- **工具提交**：用户可以提交新的AI工具

### 2.4 管理功能
- **后台管理**：管理员可以审核、编辑、删除工具信息
- **数据统计**：查看工具访问量、用户活跃度等统计信息

## 3. 技术架构

### 3.1 前端技术栈
- **框架**：React + Next.js
- **样式**：Tailwind CSS
- **状态管理**：React Query + Zustand
- **UI组件**：Shadcn/ui
- **图标**：Lucide React

### 3.2 后端服务
- **数据库**：Supabase (PostgreSQL)
- **认证**：Supabase Auth
- **存储**：Supabase Storage (用于工具图标等)
- **API**：Supabase REST API

### 3.3 部署
- **前端部署**：Vercel
- **数据库**：Supabase云服务

## 4. 数据模型设计

### 4.1 工具表 (tools)
```sql
- id: uuid (主键)
- name: varchar (工具名称)
- description: text (工具描述)
- short_description: varchar (简短描述)
- website_url: varchar (官网链接)
- logo_url: varchar (图标链接)
- category_id: uuid (分类ID，外键)
- pricing_type: enum (免费/付费/免费试用)
- pricing_details: text (价格详情)
- features: jsonb (功能特点数组)
- tags: varchar[] (标签数组)
- rating: decimal (平均评分)
- review_count: integer (评论数量)
- view_count: integer (查看次数)
- is_featured: boolean (是否推荐)
- status: enum (待审核/已发布/已下架)
- created_at: timestamp
- updated_at: timestamp
```

### 4.2 分类表 (categories)
```sql
- id: uuid (主键)
- name: varchar (分类名称)
- description: text (分类描述)
- icon: varchar (分类图标)
- slug: varchar (URL友好的标识符)
- sort_order: integer (排序)
- created_at: timestamp
```

### 4.3 用户评价表 (reviews)
```sql
- id: uuid (主键)
- tool_id: uuid (工具ID，外键)
- user_id: uuid (用户ID，外键)
- rating: integer (评分 1-5)
- comment: text (评论内容)
- created_at: timestamp
- updated_at: timestamp
```

### 4.4 用户收藏表 (favorites)
```sql
- id: uuid (主键)
- user_id: uuid (用户ID，外键)
- tool_id: uuid (工具ID，外键)
- created_at: timestamp
```

## 5. 页面结构

### 5.1 首页
- Hero区域：网站介绍和搜索框
- 推荐工具：精选的优质AI工具
- 分类导航：各个工具分类的快速入口
- 最新工具：最近添加的工具

### 5.2 分类页面
- 分类工具列表
- 筛选和排序功能
- 分页功能

### 5.3 工具详情页
- 工具基本信息
- 功能介绍
- 用户评价
- 相关推荐

### 5.4 搜索结果页
- 搜索结果列表
- 高级筛选选项

### 5.5 用户中心
- 个人收藏
- 我的评价
- 提交工具

## 6. 开发计划

### Phase 1: 基础功能 (2-3天)
- 项目初始化和环境搭建
- 数据库设计和Supabase配置
- 基础页面布局和组件开发
- 工具列表和详情页面

### Phase 2: 核心功能 (2-3天)
- 搜索和筛选功能
- 用户认证和个人中心
- 收藏和评价功能

### Phase 3: 管理功能 (1-2天)
- 后台管理界面
- 工具提交和审核流程

### Phase 4: 优化和部署 (1天)
- 性能优化
- SEO优化
- 部署上线

## 7. 成功指标
- 收录工具数量 > 100个
- 月活跃用户 > 1000人
- 用户平均停留时间 > 3分钟
- 工具点击率 > 10%
