-- 插入示例工具数据
-- 获取分类ID
DO $$
DECLARE
    text_cat_id UUID;
    image_cat_id UUID;
    code_cat_id UUID;
    audio_cat_id UUID;
    video_cat_id UUID;
    productivity_cat_id UUID;
    design_cat_id UUID;
    search_cat_id UUID;
BEGIN
    -- 获取分类ID
    SELECT id INTO text_cat_id FROM categories WHERE slug = 'text';
    SELECT id INTO image_cat_id FROM categories WHERE slug = 'image';
    SELECT id INTO code_cat_id FROM categories WHERE slug = 'code';
    SELECT id INTO audio_cat_id FROM categories WHERE slug = 'audio';
    SELECT id INTO video_cat_id FROM categories WHERE slug = 'video';
    SELECT id INTO productivity_cat_id FROM categories WHERE slug = 'productivity';
    SELECT id INTO design_cat_id FROM categories WHERE slug = 'design';
    SELECT id INTO search_cat_id FROM categories WHERE slug = 'search';

    -- 插入工具数据
    INSERT INTO tools (name, description, short_description, website_url, logo_url, category_id, pricing_type, pricing_details, features, tags, rating, review_count, view_count, is_featured, status) VALUES
    
    -- 文本生成工具
    ('ChatGPT', 'OpenAI开发的强大对话AI，能够进行自然语言对话、回答问题、协助写作等多种任务。支持多轮对话，具有强大的理解和生成能力。', '最强大的对话AI助手', 'https://chat.openai.com', 'https://via.placeholder.com/64/00D4AA/FFFFFF?text=GPT', text_cat_id, 'freemium', '免费版本 + $20/月专业版', '["自然语言对话", "代码生成", "文本创作", "问题解答", "多语言支持"]', '{对话,AI助手,文本生成}', 4.8, 1250, 15420, true, 'published'),
    
    ('Claude 3', 'Anthropic最新发布的大语言模型，在推理和创作方面表现出色。具有更好的安全性和可控性。', 'Anthropic的最新AI助手', 'https://claude.ai', 'https://via.placeholder.com/64/FF8C42/FFFFFF?text=C3', text_cat_id, 'freemium', '免费版本 + 专业版', '["高级推理", "长文本处理", "代码分析", "创意写作", "安全对话"]', '{对话AI,推理,写作}', 4.9, 156, 2340, false, 'published'),
    
    ('Notion AI', 'Notion内置的AI助手，能够帮助用户进行写作、总结、翻译等多种文档处理任务。', '智能文档助手', 'https://notion.so/ai', 'https://via.placeholder.com/64/000000/FFFFFF?text=N', text_cat_id, 'freemium', '免费试用 + $10/月', '["智能写作", "内容总结", "语言翻译", "文档整理", "模板生成"]', '{文档,写作助手,生产力}', 4.5, 750, 9800, true, 'published'),
    
    -- 图像处理工具
    ('Midjourney', '基于AI的图像生成工具，能够根据文本描述创造出令人惊艳的艺术作品和图像。', '顶级AI图像生成工具', 'https://midjourney.com', 'https://via.placeholder.com/64/FF6B6B/FFFFFF?text=MJ', image_cat_id, 'paid', '$10-60/月订阅制', '["文本到图像", "艺术创作", "高质量输出", "多种风格", "社区分享"]', '{图像生成,AI艺术,创意设计}', 4.7, 890, 12340, true, 'published'),
    
    ('DALL-E 3', 'OpenAI开发的图像生成模型，能够根据详细的文本描述生成高质量图像。', 'OpenAI的图像生成AI', 'https://openai.com/dall-e-3', 'https://via.placeholder.com/64/412991/FFFFFF?text=DE', image_cat_id, 'paid', '通过ChatGPT Plus使用', '["文本到图像", "高精度生成", "安全内容", "创意设计", "商业使用"]', '{图像生成,OpenAI,设计}', 4.6, 567, 8900, true, 'published'),
    
    ('Stable Diffusion', '开源的图像生成模型，支持本地部署和自定义训练。', '开源AI图像生成', 'https://stability.ai', 'https://via.placeholder.com/64/6366F1/FFFFFF?text=SD', image_cat_id, 'free', '开源免费，云服务付费', '["开源模型", "本地部署", "自定义训练", "插件生态", "商业友好"]', '{开源,图像生成,自定义}', 4.4, 1200, 18500, false, 'published'),
    
    -- 代码助手工具
    ('GitHub Copilot', '由GitHub和OpenAI联合开发的AI编程助手，能够实时提供代码建议和自动补全。', 'AI编程助手', 'https://github.com/features/copilot', 'https://via.placeholder.com/64/4078C0/FFFFFF?text=GH', code_cat_id, 'paid', '$10/月个人版', '["代码自动补全", "函数生成", "注释生成", "多语言支持", "IDE集成"]', '{编程,代码助手,开发工具}', 4.6, 2100, 18900, true, 'published'),
    
    ('Cursor AI', '专为程序员设计的AI代码编辑器，提供智能代码补全和生成。', 'AI代码编辑器', 'https://cursor.sh', 'https://via.placeholder.com/64/8B5CF6/FFFFFF?text=CR', code_cat_id, 'freemium', '免费版 + $20/月专业版', '["智能补全", "代码生成", "重构建议", "多语言支持", "团队协作"]', '{代码编辑器,IDE,编程助手}', 4.8, 167, 2780, false, 'published'),
    
    ('Tabnine', 'AI代码补全工具，支持多种编程语言和IDE。', 'AI代码补全助手', 'https://tabnine.com', 'https://via.placeholder.com/64/FF6B35/FFFFFF?text=TN', code_cat_id, 'freemium', '免费版 + $12/月专业版', '["智能补全", "多语言支持", "本地模型", "团队训练", "隐私保护"]', '{代码补全,编程,IDE插件}', 4.3, 890, 12400, false, 'published'),
    
    -- 音频处理工具
    ('ElevenLabs Voice', '高质量的AI语音合成工具，支持多种语言和声音克隆。', 'AI语音合成工具', 'https://elevenlabs.io', 'https://via.placeholder.com/64/F59E0B/FFFFFF?text=11', audio_cat_id, 'freemium', '免费额度 + $5-330/月', '["语音合成", "声音克隆", "多语言", "高质量输出", "API接口"]', '{语音合成,TTS,声音克隆}', 4.7, 298, 4120, false, 'published'),
    
    ('Mubert', 'AI音乐生成平台，能够创作各种风格的背景音乐。', 'AI音乐生成平台', 'https://mubert.com', 'https://via.placeholder.com/64/9333EA/FFFFFF?text=MB', audio_cat_id, 'freemium', '免费版 + $14-199/月', '["音乐生成", "多种风格", "无版权", "实时生成", "商业授权"]', '{音乐生成,背景音乐,无版权}', 4.2, 156, 2890, false, 'published'),
    
    -- 视频制作工具
    ('Runway Gen-3', '最新的AI视频生成模型，能够创建高质量的视频内容。', '下一代AI视频生成', 'https://runwayml.com', 'https://via.placeholder.com/64/6366F1/FFFFFF?text=RW', video_cat_id, 'paid', '$15-76/月', '["文本到视频", "图像到视频", "视频编辑", "特效生成", "高质量输出"]', '{视频生成,AI视频,创意}', 4.7, 89, 1890, false, 'published'),
    
    ('Synthesia', 'AI虚拟主播视频生成平台，支持多语言和自定义角色。', 'AI虚拟主播平台', 'https://synthesia.io', 'https://via.placeholder.com/64/EC4899/FFFFFF?text=SY', video_cat_id, 'paid', '$30-90/月', '["虚拟主播", "多语言支持", "自定义角色", "企业培训", "营销视频"]', '{虚拟主播,视频制作,多语言}', 4.4, 234, 3450, false, 'published'),
    
    -- 设计工具
    ('Framer AI', 'Framer集成的AI设计助手，能够快速生成网站设计和原型。', 'AI网站设计工具', 'https://framer.com', 'https://via.placeholder.com/64/EC4899/FFFFFF?text=FR', design_cat_id, 'freemium', '免费版 + $5-50/月', '["网站生成", "设计系统", "响应式设计", "原型制作", "代码导出"]', '{网站设计,UI设计,原型}', 4.5, 145, 1960, false, 'published'),
    
    ('Figma AI', 'Figma内置的AI设计助手，提供智能设计建议和自动化功能。', 'Figma AI设计助手', 'https://figma.com', 'https://via.placeholder.com/64/F24E1E/FFFFFF?text=FG', design_cat_id, 'freemium', '免费版 + $12-45/月', '["智能设计", "自动布局", "内容生成", "设计系统", "团队协作"]', '{UI设计,设计工具,协作}', 4.6, 567, 7890, false, 'published'),
    
    -- 搜索工具
    ('Perplexity Pro', '基于AI的搜索引擎，提供准确的答案和引用来源。', 'AI驱动的搜索引擎', 'https://perplexity.ai', 'https://via.placeholder.com/64/10B981/FFFFFF?text=PP', search_cat_id, 'freemium', '免费版 + $20/月专业版', '["智能搜索", "来源引用", "实时信息", "多语言支持", "对话式搜索"]', '{搜索,AI搜索,信息检索}', 4.6, 234, 3450, false, 'published'),
    
    ('You.com', 'AI搜索引擎，提供个性化搜索结果和AI助手功能。', 'AI个性化搜索', 'https://you.com', 'https://via.placeholder.com/64/0066CC/FFFFFF?text=YU', search_cat_id, 'freemium', '免费版 + $20/月专业版', '["个性化搜索", "AI助手", "隐私保护", "多模态搜索", "开发者API"]', '{搜索引擎,个性化,隐私}', 4.3, 178, 2340, false, 'published');

END $$;
