import { Search, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export function HeroSection() {
  return (
    <section className="relative py-20 lg:py-32">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* 主标题 */}
          <div className="space-y-4">
            <h1 className="text-4xl lg:text-6xl font-bold tracking-tight">
              发现最好的
              <span className="text-primary"> AI工具</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              精心收录各类优质AI工具，帮助你提升工作效率，释放创造力
            </p>
          </div>

          {/* 搜索框 */}
          <div className="max-w-2xl mx-auto">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                <Input
                  placeholder="搜索你需要的AI工具..."
                  className="pl-12 h-14 text-lg"
                />
              </div>
              <Button size="lg" className="h-14 px-8">
                搜索
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* 热门搜索标签 */}
          <div className="flex flex-wrap justify-center gap-2">
            <span className="text-sm text-muted-foreground">热门搜索：</span>
            {['ChatGPT', 'Midjourney', 'GitHub Copilot', 'Notion AI', 'Figma AI'].map((tag) => (
              <Button
                key={tag}
                variant="outline"
                size="sm"
                className="h-8 text-xs"
              >
                {tag}
              </Button>
            ))}
          </div>

          {/* 统计数据 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-8">
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">500+</div>
              <div className="text-sm text-muted-foreground">AI工具</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">20+</div>
              <div className="text-sm text-muted-foreground">工具分类</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">10K+</div>
              <div className="text-sm text-muted-foreground">用户</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">50K+</div>
              <div className="text-sm text-muted-foreground">月访问量</div>
            </div>
          </div>
        </div>
      </div>

      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="h-[600px] w-[600px] rounded-full bg-primary/5 blur-3xl"></div>
        </div>
      </div>
    </section>
  )
}
