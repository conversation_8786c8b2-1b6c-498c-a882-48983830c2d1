'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Clock, ExternalLink, Star } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tool } from '@/lib/supabase'

// 模拟最新工具数据
const mockLatestTools: Tool[] = [
  {
    id: '5',
    name: 'Claude 3',
    description: 'Anthropic最新发布的大语言模型，在推理和创作方面表现出色。',
    short_description: 'Anthropic的最新AI助手',
    website_url: 'https://claude.ai',
    logo_url: 'https://via.placeholder.com/64/FF8C42/FFFFFF?text=C3',
    category_id: '1',
    pricing_type: 'freemium',
    pricing_details: '免费版本 + 专业版',
    features: ['高级推理', '长文本处理', '代码分析', '创意写作'],
    tags: ['对话AI', '推理', '写作'],
    rating: 4.9,
    review_count: 156,
    view_count: 2340,
    is_featured: false,
    status: 'published',
    created_at: '2024-01-15',
    updated_at: '2024-01-15'
  },
  {
    id: '6',
    name: 'Runway Gen-3',
    description: '最新的AI视频生成模型，能够创建高质量的视频内容。',
    short_description: '下一代AI视频生成',
    website_url: 'https://runwayml.com',
    logo_url: 'https://via.placeholder.com/64/6366F1/FFFFFF?text=RW',
    category_id: '5',
    pricing_type: 'paid',
    pricing_details: '$15-76/月',
    features: ['文本到视频', '图像到视频', '视频编辑', '特效生成'],
    tags: ['视频生成', 'AI视频', '创意'],
    rating: 4.7,
    review_count: 89,
    view_count: 1890,
    is_featured: false,
    status: 'published',
    created_at: '2024-01-12',
    updated_at: '2024-01-12'
  },
  {
    id: '7',
    name: 'Perplexity Pro',
    description: '基于AI的搜索引擎，提供准确的答案和引用来源。',
    short_description: 'AI驱动的搜索引擎',
    website_url: 'https://perplexity.ai',
    logo_url: 'https://via.placeholder.com/64/10B981/FFFFFF?text=PP',
    category_id: '10',
    pricing_type: 'freemium',
    pricing_details: '免费版 + $20/月专业版',
    features: ['智能搜索', '来源引用', '实时信息', '多语言支持'],
    tags: ['搜索', 'AI搜索', '信息检索'],
    rating: 4.6,
    review_count: 234,
    view_count: 3450,
    is_featured: false,
    status: 'published',
    created_at: '2024-01-10',
    updated_at: '2024-01-10'
  },
  {
    id: '8',
    name: 'Cursor AI',
    description: '专为程序员设计的AI代码编辑器，提供智能代码补全和生成。',
    short_description: 'AI代码编辑器',
    website_url: 'https://cursor.sh',
    logo_url: 'https://via.placeholder.com/64/8B5CF6/FFFFFF?text=CR',
    category_id: '3',
    pricing_type: 'freemium',
    pricing_details: '免费版 + $20/月专业版',
    features: ['智能补全', '代码生成', '重构建议', '多语言支持'],
    tags: ['代码编辑器', 'IDE', '编程助手'],
    rating: 4.8,
    review_count: 167,
    view_count: 2780,
    is_featured: false,
    status: 'published',
    created_at: '2024-01-08',
    updated_at: '2024-01-08'
  },
  {
    id: '9',
    name: 'ElevenLabs Voice',
    description: '高质量的AI语音合成工具，支持多种语言和声音克隆。',
    short_description: 'AI语音合成工具',
    website_url: 'https://elevenlabs.io',
    logo_url: 'https://via.placeholder.com/64/F59E0B/FFFFFF?text=11',
    category_id: '4',
    pricing_type: 'freemium',
    pricing_details: '免费额度 + $5-330/月',
    features: ['语音合成', '声音克隆', '多语言', '高质量输出'],
    tags: ['语音合成', 'TTS', '声音克隆'],
    rating: 4.7,
    review_count: 298,
    view_count: 4120,
    is_featured: false,
    status: 'published',
    created_at: '2024-01-05',
    updated_at: '2024-01-05'
  },
  {
    id: '10',
    name: 'Framer AI',
    description: 'Framer集成的AI设计助手，能够快速生成网站设计和原型。',
    short_description: 'AI网站设计工具',
    website_url: 'https://framer.com',
    logo_url: 'https://via.placeholder.com/64/EC4899/FFFFFF?text=FR',
    category_id: '7',
    pricing_type: 'freemium',
    pricing_details: '免费版 + $5-50/月',
    features: ['网站生成', '设计系统', '响应式设计', '原型制作'],
    tags: ['网站设计', 'UI设计', '原型'],
    rating: 4.5,
    review_count: 145,
    view_count: 1960,
    is_featured: false,
    status: 'published',
    created_at: '2024-01-03',
    updated_at: '2024-01-03'
  }
]

export function LatestTools() {
  const [tools, setTools] = useState<Tool[]>([])

  useEffect(() => {
    // 模拟API调用
    setTools(mockLatestTools)
  }, [])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return '1天前'
    if (diffDays < 7) return `${diffDays}天前`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)}周前`
    return `${Math.ceil(diffDays / 30)}月前`
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">最新收录</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            最近添加的优质AI工具，第一时间了解最新趋势
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool) => (
            <Card key={tool.id} className="group hover:shadow-lg transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                      <img 
                        src={tool.logo_url} 
                        alt={tool.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm text-muted-foreground">
                            {tool.rating}
                          </span>
                        </div>
                        <span className="text-muted-foreground">•</span>
                        <div className="flex items-center space-x-1 text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span className="text-xs">
                            {formatDate(tool.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    新
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <CardDescription className="line-clamp-2">
                  {tool.short_description}
                </CardDescription>

                <div className="flex flex-wrap gap-1">
                  {tool.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      tool.pricing_type === 'free' 
                        ? 'bg-green-100 text-green-800' 
                        : tool.pricing_type === 'freemium'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {tool.pricing_type === 'free' ? '免费' : 
                       tool.pricing_type === 'freemium' ? '免费试用' : '付费'}
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/tools/${tool.id}`}>
                        详情
                      </Link>
                    </Button>
                    <Button size="sm" asChild>
                      <Link href={tool.website_url} target="_blank">
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="outline" size="lg" asChild>
            <Link href="/tools?sort=latest">
              查看更多最新工具
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
