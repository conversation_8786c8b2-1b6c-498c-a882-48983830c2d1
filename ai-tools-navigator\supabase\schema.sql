-- 创建分类表
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  icon VARCHAR(50),
  slug VARCHAR(100) UNIQUE NOT NULL,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建工具表
CREATE TABLE tools (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  short_description VARCHAR(500),
  website_url VARCHAR(500) NOT NULL,
  logo_url VARCHAR(500),
  category_id UUID REFERENCES categories(id),
  pricing_type VARCHAR(20) CHECK (pricing_type IN ('free', 'paid', 'freemium')) DEFAULT 'free',
  pricing_details TEXT,
  features JSONB DEFAULT '[]',
  tags TEXT[] DEFAULT '{}',
  rating DECIMAL(3,2) DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) CHECK (status IN ('pending', 'published', 'archived')) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建用户评价表
CREATE TABLE reviews (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tool_id UUID REFERENCES tools(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tool_id, user_id)
);

-- 创建用户收藏表
CREATE TABLE favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tool_id UUID REFERENCES tools(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tool_id)
);

-- 创建索引
CREATE INDEX idx_tools_category_id ON tools(category_id);
CREATE INDEX idx_tools_status ON tools(status);
CREATE INDEX idx_tools_is_featured ON tools(is_featured);
CREATE INDEX idx_tools_rating ON tools(rating DESC);
CREATE INDEX idx_tools_created_at ON tools(created_at DESC);
CREATE INDEX idx_reviews_tool_id ON reviews(tool_id);
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_favorites_user_id ON favorites(user_id);
CREATE INDEX idx_favorites_tool_id ON favorites(tool_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为tools表创建更新时间触发器
CREATE TRIGGER update_tools_updated_at BEFORE UPDATE ON tools
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为reviews表创建更新时间触发器
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建更新工具评分的函数
CREATE OR REPLACE FUNCTION update_tool_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE tools 
    SET 
        rating = (
            SELECT COALESCE(AVG(rating), 0) 
            FROM reviews 
            WHERE tool_id = COALESCE(NEW.tool_id, OLD.tool_id)
        ),
        review_count = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE tool_id = COALESCE(NEW.tool_id, OLD.tool_id)
        )
    WHERE id = COALESCE(NEW.tool_id, OLD.tool_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- 创建评分更新触发器
CREATE TRIGGER update_tool_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_tool_rating();

-- 插入默认分类数据
INSERT INTO categories (name, description, icon, slug, sort_order) VALUES
('文本生成', '写作助手、内容创作、翻译工具', 'MessageSquare', 'text', 1),
('图像处理', '图像生成、编辑、设计工具', 'Image', 'image', 2),
('代码助手', '编程辅助、代码生成工具', 'Code', 'code', 3),
('音频处理', '语音合成、音乐生成工具', 'Music', 'audio', 4),
('视频制作', '视频生成、编辑、特效工具', 'Video', 'video', 5),
('生产力工具', '办公助手、自动化工具', 'FileText', 'productivity', 6),
('设计工具', 'UI设计、创意设计工具', 'Palette', 'design', 7),
('研究分析', '数据分析、研究助手', 'Brain', 'research', 8),
('语音识别', '语音转文字、语音助手', 'Mic', 'voice', 9),
('搜索工具', '智能搜索、信息检索', 'Search', 'search', 10),
('数据分析', '商业智能、数据可视化', 'BarChart', 'analytics', 11),
('自动化', '工作流自动化、机器人流程', 'Zap', 'automation', 12);

-- 启用行级安全策略
ALTER TABLE tools ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
-- 所有人都可以查看已发布的工具
CREATE POLICY "Anyone can view published tools" ON tools
    FOR SELECT USING (status = 'published');

-- 认证用户可以插入工具（待审核状态）
CREATE POLICY "Authenticated users can insert tools" ON tools
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 认证用户可以查看和管理自己的评价
CREATE POLICY "Users can view all reviews" ON reviews
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own reviews" ON reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" ON reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews" ON reviews
    FOR DELETE USING (auth.uid() = user_id);

-- 认证用户可以管理自己的收藏
CREATE POLICY "Users can view their own favorites" ON favorites
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own favorites" ON favorites
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own favorites" ON favorites
    FOR DELETE USING (auth.uid() = user_id);
