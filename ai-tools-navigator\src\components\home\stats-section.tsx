import { TrendingUp, Users, Star, Zap } from 'lucide-react'

export function StatsSection() {
  const stats = [
    {
      icon: TrendingUp,
      value: '500+',
      label: 'AI工具收录',
      description: '精心筛选的优质工具'
    },
    {
      icon: Users,
      value: '10K+',
      label: '活跃用户',
      description: '每月使用我们的服务'
    },
    {
      icon: Star,
      value: '4.9',
      label: '用户评分',
      description: '基于真实用户反馈'
    },
    {
      icon: Zap,
      value: '99%',
      label: '工具可用性',
      description: '确保链接有效性'
    }
  ]

  return (
    <section className="py-16 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">为什么选择我们</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            我们致力于为用户提供最全面、最准确的AI工具信息
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="text-center space-y-4">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10">
                  <Icon className="h-8 w-8 text-primary" />
                </div>
                <div className="space-y-2">
                  <div className="text-4xl font-bold text-primary">{stat.value}</div>
                  <div className="font-semibold">{stat.label}</div>
                  <div className="text-sm text-muted-foreground">{stat.description}</div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
