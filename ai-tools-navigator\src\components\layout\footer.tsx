import Link from 'next/link'
import { Github, Twitter, Mail } from 'lucide-react'

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 网站信息 */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 rounded bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">AI</span>
              </div>
              <span className="font-bold">Tools Navigator</span>
            </div>
            <p className="text-sm text-muted-foreground">
              发现最好的AI工具，提升你的工作效率
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-muted-foreground hover:text-primary">
                <Github className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-primary">
                <Twitter className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-primary">
                <Mail className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* 工具分类 */}
          <div className="space-y-4">
            <h3 className="font-semibold">工具分类</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/categories/text" className="text-muted-foreground hover:text-primary">文本生成</Link></li>
              <li><Link href="/categories/image" className="text-muted-foreground hover:text-primary">图像处理</Link></li>
              <li><Link href="/categories/code" className="text-muted-foreground hover:text-primary">代码助手</Link></li>
              <li><Link href="/categories/audio" className="text-muted-foreground hover:text-primary">音频处理</Link></li>
            </ul>
          </div>

          {/* 快速链接 */}
          <div className="space-y-4">
            <h3 className="font-semibold">快速链接</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/submit" className="text-muted-foreground hover:text-primary">提交工具</Link></li>
              <li><Link href="/about" className="text-muted-foreground hover:text-primary">关于我们</Link></li>
              <li><Link href="/contact" className="text-muted-foreground hover:text-primary">联系我们</Link></li>
              <li><Link href="/privacy" className="text-muted-foreground hover:text-primary">隐私政策</Link></li>
            </ul>
          </div>

          {/* 联系信息 */}
          <div className="space-y-4">
            <h3 className="font-semibold">联系我们</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>邮箱: <EMAIL></li>
              <li>微信: aitoolsnav</li>
              <li>QQ群: 123456789</li>
            </ul>
          </div>
        </div>

        <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 AI Tools Navigator. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
