'use client'

import Link from 'next/link'
import { Search, Menu, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useState } from 'react'

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">AI</span>
            </div>
            <span className="font-bold text-xl">Tools Navigator</span>
          </Link>

          {/* 搜索框 - 桌面版 */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索AI工具..."
                className="pl-10"
              />
            </div>
          </div>

          {/* 导航菜单 - 桌面版 */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/categories" className="text-sm font-medium hover:text-primary">
              分类
            </Link>
            <Link href="/submit" className="text-sm font-medium hover:text-primary">
              提交工具
            </Link>
            <Link href="/about" className="text-sm font-medium hover:text-primary">
              关于
            </Link>
            <Button variant="outline" size="sm">
              <User className="h-4 w-4 mr-2" />
              登录
            </Button>
          </nav>

          {/* 移动端菜单按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        {/* 移动端搜索框 */}
        <div className="md:hidden pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="搜索AI工具..."
              className="pl-10"
            />
          </div>
        </div>

        {/* 移动端导航菜单 */}
        {isMenuOpen && (
          <div className="md:hidden border-t py-4">
            <nav className="flex flex-col space-y-4">
              <Link href="/categories" className="text-sm font-medium hover:text-primary">
                分类
              </Link>
              <Link href="/submit" className="text-sm font-medium hover:text-primary">
                提交工具
              </Link>
              <Link href="/about" className="text-sm font-medium hover:text-primary">
                关于
              </Link>
              <Button variant="outline" size="sm" className="w-fit">
                <User className="h-4 w-4 mr-2" />
                登录
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
