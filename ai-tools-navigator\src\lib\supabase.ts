import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 数据库类型定义
export interface Tool {
  id: string
  name: string
  description: string
  short_description: string
  website_url: string
  logo_url: string
  category_id: string
  pricing_type: 'free' | 'paid' | 'freemium'
  pricing_details?: string
  features: string[]
  tags: string[]
  rating: number
  review_count: number
  view_count: number
  is_featured: boolean
  status: 'pending' | 'published' | 'archived'
  created_at: string
  updated_at: string
  category?: Category
}

export interface Category {
  id: string
  name: string
  description: string
  icon: string
  slug: string
  sort_order: number
  created_at: string
}

export interface Review {
  id: string
  tool_id: string
  user_id: string
  rating: number
  comment: string
  created_at: string
  updated_at: string
}

export interface Favorite {
  id: string
  user_id: string
  tool_id: string
  created_at: string
}
